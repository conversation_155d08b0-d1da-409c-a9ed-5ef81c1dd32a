import "dotenv/config";
import { <PERSON>o } from "hono";
import authRoute from "./routes/auth";
import { createInvoice } from "./routes/create-invoice";
import { xenditWebhook } from "./routes/xendit-webhook";
import dataRoute from "./routes/data";
import { cors } from "hono/cors";
import { authMiddleware } from "./middleware/auth";
import { getCorsOrigins, getCorsAllowHeaders, getCorsAllowMethods, getPort } from "./utils/env";

const app = new Hono();

// CORS Configuration from environment variables
const corsOrigins = getCorsOrigins();
const corsAllowHeaders = getCorsAllowHeaders();
const corsAllowMethods = getCorsAllowMethods();

app.use(
  "*",
  cors({
    origin: [...corsOrigins, "*", "null"],
    allowHeaders: corsAllowHeaders,
    allowMethods: corsAllowMethods,
    credentials: true,
  })
);

// Health check endpoint
app.get("/health", (c) => {
  return c.json({ status: "ok", timestamp: new Date().toISOString() });
});

app.route("/", authRoute);
app.route("/api/data", dataRoute);

app.use("/create-invoice", authMiddleware);

app.post("/create-invoice", createInvoice);
app.post("/xendit-webhook", xenditWebhook);

const port = getPort();

console.log(`Server is running on port ${port}`);

// Export app for type inference
export { app };

// For Bun/Cloudflare Workers
export default {
  port,
  fetch: app.fetch,
};

// For Node.js deployment
if (typeof Bun === "undefined") {
  import("@hono/node-server").then(({ serve }) => {
    const server = serve({
      fetch: app.fetch,
      port: Number(port),
    });

    console.log(`HTTP server started on port ${port}`);

    // Keep the process alive
    process.on('SIGTERM', () => {
      console.log('Received SIGTERM, shutting down gracefully');
      server.close(() => {
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('Received SIGINT, shutting down gracefully');
      server.close(() => {
        process.exit(0);
      });
    });
  }).catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}